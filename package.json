{"name": "backend-driven-cms", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@apollo/client": "^3.13.8", "@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-react": "^11.0.0", "@hookform/resolvers": "^5.2.0", "@oneassure-tech/oa-utilities": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@types/lodash": "^4.17.20", "@types/multer": "^2.0.0", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "aws-sdk": "^2.1692.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "ioredis": "^5.6.1", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "multer": "^2.0.2", "next": "15.4.4", "next-themes": "^0.4.6", "perf_hooks": "^0.0.1", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "react-window": "^1.8.11", "sonner": "^1.6.0", "tailwind-merge": "^2.5.4", "uuid": "^11.1.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/styled-components": "^5.1.34", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "typescript": "^5"}}