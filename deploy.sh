#!/bin/bash

set -ex
ENV_NAME=$1
OVERARCHING_ENV=$2
PVT_URL=$3

# construct GCR_URI based on ENV_NAME
if [ "$ENV_NAME" == "prod" ]; then
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-prod/oneassure"
else
    GCR_URI="asia-south1-docker.pkg.dev/oneassure-non-prod/oneassure-non-prod"
fi

APP_NAME="hasura-crud-studio"

TAG=$APP_NAME-$(git rev-parse --abbrev-ref HEAD)-$(git describe --always)

docker build \
    --target deps \
    -t oa-$APP_NAME-deps:$TAG \
    . || exit 1

docker build \
    --target builder \
    --build-arg env=$ENV_NAME \
    -t oa-$APP_NAME-builder:$TAG \
    . || exit 1

docker build \
    --build-arg env=$ENV_NAME \
    -t $GCR_URI/oa-$APP_NAME-$ENV_NAME:$TAG \
    . || exit 1

docker push $GCR_URI/oa-$APP_NAME-$ENV_NAME:$TAG || exit 1

(
    sed \
        -e "s/{{env_name}}/$ENV_NAME/g" \
        -e "s/{{image_id}}/$TAG/g" \
        -e "s/{{app_name}}/$APP_NAME/g" \
        -e "s/{{overarching_env}}/$OVERARCHING_ENV/g" \
        -e "s/{{pvt_url}}/$PVT_URL/g" \
        -e "s|{{gcr_uri}}|$GCR_URI|g" \
        k8s/manifest.yaml | kubectl apply -f -
) || exit 1

kubectl rollout status deployment/oa-$APP_NAME-deployment-$ENV_NAME -n oa-$ENV_NAME || exit 1

set +ex